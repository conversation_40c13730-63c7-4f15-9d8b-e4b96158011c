#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浙江省预警信息爬虫 - 完整单文件版本
集成所有功能：自动密码管理、数据获取、解析、保存
只需要这一个文件就能完整运行！
"""

import requests
import json
import re
import time
import os
from datetime import datetime

# 图片生成相关库
try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.font_manager import FontProperties
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️  matplotlib未安装，无法生成图片。运行 'pip install matplotlib' 安装后可使用图片功能")


class WeatherWarningCrawler:
    def __init__(self):
        self.base_url = "https://www.12379zj.cn"
        self.config_js_url = f"{self.base_url}/zjemw/resources/newClient/js/map/config/config.js"
        self.api_url = f"{self.base_url}/api/share"
        self.cache_file = "password_cache.json"
        self.cache_duration = 3600  # 1小时缓存
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://www.12379zj.cn',
            'Referer': 'https://www.12379zj.cn/map/index/toIndex'
        }
    
    def get_password_from_official(self):
        """从官方配置文件获取最新密码"""
        try:
            print("正在从官方配置文件获取密码...")
            response = requests.get(self.config_js_url, timeout=10)
            response.raise_for_status()
            
            js_content = response.text
            password_pattern = r"'apiPassword'\s*:\s*'([^']+)'"
            match = re.search(password_pattern, js_content)
            
            if match:
                password = match.group(1)
                print(f"✅ 成功获取密码 (长度: {len(password)})")
                return password
            else:
                print("❌ 未在配置文件中找到密码")
                return None
                
        except Exception as e:
            print(f"❌ 获取配置文件失败: {e}")
            return None
    
    def load_cache(self):
        """加载缓存的密码"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache = json.load(f)
            
            cache_time = cache.get('timestamp', 0)
            if time.time() - cache_time < self.cache_duration:
                return cache.get('password')
            else:
                print("缓存已过期")
                return None
                
        except FileNotFoundError:
            return None
        except Exception as e:
            print(f"读取缓存失败: {e}")
            return None
    
    def save_cache(self, password):
        """保存密码到缓存"""
        try:
            cache = {
                'password': password,
                'timestamp': time.time(),
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def test_password(self, password):
        """测试密码是否有效"""
        data = {
            'userCode': 'ZJWechat',
            'password': password,
            'token': '1212232323',
            'identifier': 'iden11323232',
            'dataformat': 'JSON',
            'interfaceCode': 'A0001',
            'params': '{"isChildrens":true,"alarmType":2}'
        }
        
        try:
            response = requests.post(self.api_url, headers=self.headers, data=data, timeout=10)
            result = response.json()
            
            if result.get('returnCode') == '0':
                return True, result.get('total', 0)
            else:
                return False, result.get('returnMessage', '未知错误')
                
        except Exception as e:
            return False, str(e)
    
    def get_valid_password(self):
        """获取有效的密码"""
        # 1. 尝试缓存
        cached_password = self.load_cache()
        if cached_password:
            is_valid, result = self.test_password(cached_password)
            if is_valid:
                return cached_password
        
        # 2. 从官方获取
        new_password = self.get_password_from_official()
        if new_password:
            is_valid, result = self.test_password(new_password)
            if is_valid:
                self.save_cache(new_password)
                return new_password
        
        # 3. 备用密码
        fallback_password = "CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8="
        is_valid, result = self.test_password(fallback_password)
        if is_valid:
            return fallback_password
        
        return None
    
    def get_warning_data(self, alarm_type=2, is_childrens=True, retry_count=3):
        """获取预警数据"""
        for attempt in range(retry_count):
            password = self.get_valid_password()
            
            if not password:
                print(f"❌ 第{attempt+1}次尝试：无法获取有效密码")
                if attempt < retry_count - 1:
                    time.sleep(10)
                continue
            
            data = {
                'userCode': 'ZJWechat',
                'password': password,
                'token': '1212232323',
                'identifier': 'iden11323232',
                'dataformat': 'JSON',
                'interfaceCode': 'A0001',
                'params': json.dumps({
                    "isChildrens": is_childrens,
                    "alarmType": alarm_type
                })
            }
            
            try:
                print(f"第{attempt+1}次尝试获取预警数据...")
                response = requests.post(self.api_url, headers=self.headers, data=data, timeout=30)
                response.raise_for_status()
                
                result = response.json()
                
                if result.get('returnCode') == '0':
                    print(f"✅ 成功获取数据，共 {result.get('total', 0)} 条预警信息")
                    return result
                else:
                    error_msg = result.get('returnMessage', '未知错误')
                    print(f"❌ API返回错误: {error_msg}")
                    
                    if '密码' in error_msg or 'password' in error_msg.lower():
                        try:
                            os.remove(self.cache_file)
                        except:
                            pass
                        
                        if attempt < retry_count - 1:
                            time.sleep(5)
                        continue
                    else:
                        return None
                        
            except Exception as e:
                print(f"❌ 请求失败: {e}")
                if attempt < retry_count - 1:
                    time.sleep(5)
                continue
        
        return None
    
    def parse_warning_data(self, data):
        """解析预警数据"""
        if not data or 'data' not in data:
            return []
        
        warnings = []
        for item in data['data']:
            alert = item.get('alert', {})
            info = alert.get('info', {})
            area = info.get('area', {})
            
            warning_info = {
                'title': info.get('headline', ''),
                'sender': alert.get('sender', ''),
                'time': alert.get('sendTime', ''),
                'level': info.get('severity', ''),
                'type': info.get('disaName', ''),
                'area': area.get('areaDesc', ''),
                'expires': info.get('expires', ''),
                'description': info.get('description', ''),
                'identifier': alert.get('identifier', ''),
                'instruction': info.get('instruction', ''),
                'sender_name': info.get('senderName', '')
            }
            warnings.append(warning_info)
        
        return warnings
    
    def display_warnings(self, warnings):
        """显示预警信息"""
        if not warnings:
            print("当前没有预警信息")
            return
        
        print(f"\n浙江省预警信息 (共{len(warnings)}条)")
        print("=" * 80)
        
        for i, warning in enumerate(warnings, 1):
            print(f"\n{i}. {warning['title']}")
            print(f"   发布机构: {warning['sender']}")
            print(f"   发布时间: {warning['time']}")
            print(f"   预警等级: {warning['level']}")
            print(f"   灾害类型: {warning['type']}")
            print(f"   影响区域: {warning['area']}")
            print(f"   有效期至: {warning['expires']}")
            
            description = warning['description']
            if description:
                desc_short = description[:100] + "..." if len(description) > 100 else description
                print(f"   详细信息: {desc_short}")
            
            print("-" * 80)
    
    def save_to_json(self, warnings, filename=None):
        """保存为JSON格式"""
        if not warnings:
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"warnings_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(warnings, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 数据已保存到: {filename}")
    
    def get_statistics(self, warnings):
        """获取统计信息"""
        if not warnings:
            return {}
        
        stats = {
            'total': len(warnings),
            'by_type': {},
            'by_level': {},
            'by_sender': {}
        }
        
        for warning in warnings:
            # 按类型统计
            warning_type = warning['type']
            stats['by_type'][warning_type] = stats['by_type'].get(warning_type, 0) + 1
            
            # 按等级统计
            level = warning['level']
            stats['by_level'][level] = stats['by_level'].get(level, 0) + 1
            
            # 按发布机构统计
            sender = warning['sender']
            stats['by_sender'][sender] = stats['by_sender'].get(sender, 0) + 1
        
        return stats
    
    def get_level_priority(self, level):
        """获取预警等级优先级（数字越大等级越高）"""
        level_map = {
            'Red': 4,      # 红色 - 最高级
            'Orange': 3,   # 橙色
            'Yellow': 2,   # 黄色
            'Blue': 1,     # 蓝色 - 最低级
            'White': 0     # 白色或其他
        }
        return level_map.get(level, 0)

    def get_level_color_name(self, level):
        """获取预警等级的中文颜色名称"""
        color_map = {
            'Red': '红色',
            'Orange': '橙色',
            'Yellow': '黄色',
            'Blue': '蓝色',
            'White': '白色'
        }
        return color_map.get(level, level)

    def print_table_format(self, warnings):
        """以表格形式显示预警信息，按颜色等级排序，自适应列宽"""
        if not warnings:
            print("当前没有预警信息")
            return

        # 按预警等级排序（红色最高，蓝色最低）
        sorted_warnings = sorted(warnings, key=lambda x: self.get_level_priority(x['level']), reverse=True)

        print(f"\n🚨 浙江省预警信息表格 (共{len(warnings)}条)")

        # 计算每列的最大宽度
        headers = ['预警等级', '预警类型', '发布机构']
        col_widths = [len(header) for header in headers]

        # 准备数据并计算宽度
        table_data = []
        for warning in sorted_warnings:
            level_color = self.get_level_color_name(warning['level'])
            warning_type = warning['type']
            sender = warning['sender']

            # 根据等级添加颜色标识
            if warning['level'] == 'Red':
                level_display = f"🔴{level_color}"
            elif warning['level'] == 'Orange':
                level_display = f"🟠{level_color}"
            elif warning['level'] == 'Yellow':
                level_display = f"🟡{level_color}"
            elif warning['level'] == 'Blue':
                level_display = f"🔵{level_color}"
            else:
                level_display = f"⚪{level_color}"

            row = [level_display, warning_type, sender]
            table_data.append(row)

            # 更新列宽（考虑中文字符和emoji的显示宽度）
            col_widths[0] = max(col_widths[0], len(level_color) + 2)  # emoji + 中文
            col_widths[1] = max(col_widths[1], len(warning_type))
            col_widths[2] = max(col_widths[2], len(sender))

        # 设置更紧凑的最大宽度
        col_widths[0] = min(col_widths[0], 6)   # 预警等级最大6字符
        col_widths[1] = min(col_widths[1], 6)   # 预警类型最大6字符
        col_widths[2] = min(col_widths[2], 20)  # 发布机构最大20字符

        # 生成表格边框
        top_border = "┌" + "┬".join("─" * (w + 2) for w in col_widths) + "┐"
        middle_border = "├" + "┼".join("─" * (w + 2) for w in col_widths) + "┤"
        bottom_border = "└" + "┴".join("─" * (w + 2) for w in col_widths) + "┘"

        print("=" * len(top_border))
        print(top_border)

        # 表格头部
        header_row = "│"
        for i, header in enumerate(headers):
            header_row += f" {header:<{col_widths[i]}} │"
        print(header_row)
        print(middle_border)

        # 表格内容
        for row in table_data:
            data_row = "│"
            for i, cell in enumerate(row):
                # 处理过长的内容
                if len(cell) > col_widths[i]:
                    cell = cell[:col_widths[i]-3] + "..."
                data_row += f" {cell:<{col_widths[i]}} │"
            print(data_row)

        print(bottom_border)

        # 统计信息
        self.print_level_statistics(warnings)

    def print_level_statistics(self, warnings):
        """打印按等级统计的信息"""
        level_stats = {}
        for warning in warnings:
            level = warning['level']
            level_stats[level] = level_stats.get(level, 0) + 1

        print(f"\n📊 预警等级统计")
        print("─" * 40)

        # 按等级优先级排序
        sorted_levels = sorted(level_stats.items(), key=lambda x: self.get_level_priority(x[0]), reverse=True)

        for level, count in sorted_levels:
            level_color = self.get_level_color_name(level)
            if level == 'Red':
                icon = "🔴"
            elif level == 'Orange':
                icon = "🟠"
            elif level == 'Yellow':
                icon = "🟡"
            elif level == 'Blue':
                icon = "🔵"
            else:
                icon = "⚪"

            print(f"{icon} {level_color}预警: {count}条")

        # 类型统计
        type_stats = {}
        for warning in warnings:
            warning_type = warning['type']
            type_stats[warning_type] = type_stats.get(warning_type, 0) + 1

        print(f"\n📋 预警类型统计")
        print("─" * 40)
        for warning_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"⚡ {warning_type}: {count}条")

    def generate_warning_image(self, warnings, filename=None):
        """生成预警信息表格图片"""
        if not MATPLOTLIB_AVAILABLE:
            print("❌ matplotlib未安装，无法生成图片")
            return None

        if not warnings:
            print("没有预警数据，无法生成图片")
            return None

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 按预警等级排序
        sorted_warnings = sorted(warnings, key=lambda x: self.get_level_priority(x['level']), reverse=True)

        # 准备表格数据并计算自适应列宽
        table_data = []
        colors = []

        # 表头
        headers = ['预警等级', '预警类型', '发布机构', '发布时间']
        table_data.append(headers)
        colors.append(['#4472C4'] * len(headers))  # 表头颜色

        # 计算每列内容的最大长度
        col_max_lengths = [len(header) for header in headers]

        # 数据行
        data_rows = []
        for warning in sorted_warnings:
            level_color = self.get_level_color_name(warning['level'])
            warning_type = warning['type']
            sender = warning['sender']
            time_str = warning['time'][:16] if warning['time'] else ''  # 只显示日期和时间

            row = [level_color, warning_type, sender, time_str]
            data_rows.append(row)
            table_data.append(row)

            # 更新每列的最大长度
            for i, cell in enumerate(row):
                col_max_lengths[i] = max(col_max_lengths[i], len(str(cell)))

        # 计算真正紧凑的自适应列宽
        # 只添加最小必要的边距
        padding = 1  # 每列最小边距

        # 根据实际内容设置最小宽度（更紧凑）
        min_widths = [
            max(4, col_max_lengths[0] + padding),  # 预警等级：实际内容+边距，最小4
            max(3, col_max_lengths[1] + padding),  # 预警类型：实际内容+边距，最小3
            max(6, col_max_lengths[2] + padding),  # 发布机构：实际内容+边距，最小6
            max(4, col_max_lengths[3] + padding)   # 发布时间：实际内容+边距，最小4（更窄）
        ]

        # 计算紧凑的列宽比例
        total_length = sum(min_widths)
        col_widths = [width / total_length for width in min_widths]

        # 根据实际内容调整图片大小，更紧凑
        estimated_width = max(6, min(10, total_length * 0.1))
        fig, ax = plt.subplots(figsize=(estimated_width, max(6, len(warnings) * 0.6)))
        ax.axis('off')

        # 标题
        title = f"浙江省预警信息表格 (共{len(warnings)}条)"
        fig.suptitle(title, fontsize=20, fontweight='bold', y=0.95)

        # 为每行设置颜色
        for warning in sorted_warnings:
            # 根据预警等级设置行颜色
            if warning['level'] == 'Red':
                row_color = ['#FFE6E6'] * len(headers)  # 浅红色
            elif warning['level'] == 'Orange':
                row_color = ['#FFF2E6'] * len(headers)  # 浅橙色
            elif warning['level'] == 'Yellow':
                row_color = ['#FFFBE6'] * len(headers)  # 浅黄色
            elif warning['level'] == 'Blue':
                row_color = ['#E6F3FF'] * len(headers)  # 浅蓝色
            else:
                row_color = ['#F8F9FA'] * len(headers)  # 浅灰色

            colors.append(row_color)

        # 创建表格，使用自适应列宽
        table = ax.table(cellText=table_data[1:],
                        colLabels=table_data[0],
                        cellLoc='center',
                        loc='center',
                        colWidths=col_widths)

        # 设置表格样式
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)

        # 设置表头样式
        for i in range(len(headers)):
            table[(0, i)].set_facecolor('#4472C4')
            table[(0, i)].set_text_props(weight='bold', color='white')
            table[(0, i)].set_height(0.08)

        # 设置数据行样式
        for i in range(1, len(table_data)):
            for j in range(len(headers)):
                table[(i, j)].set_facecolor(colors[i][j])
                table[(i, j)].set_height(0.06)

                # 预警等级列添加颜色标识
                if j == 0:  # 预警等级列（现在是第0列）
                    level = sorted_warnings[i-1]['level']
                    if level == 'Red':
                        table[(i, j)].set_text_props(color='red', weight='bold')
                    elif level == 'Orange':
                        table[(i, j)].set_text_props(color='orange', weight='bold')
                    elif level == 'Yellow':
                        table[(i, j)].set_text_props(color='#DAA520', weight='bold')
                    elif level == 'Blue':
                        table[(i, j)].set_text_props(color='blue', weight='bold')

        # 添加统计信息
        level_stats = {}
        type_stats = {}
        for warning in warnings:
            level = warning['level']
            warning_type = warning['type']
            level_stats[level] = level_stats.get(level, 0) + 1
            type_stats[warning_type] = type_stats.get(warning_type, 0) + 1

        # 在图片底部添加统计信息
        stats_text = "预警等级统计: "
        sorted_levels = sorted(level_stats.items(), key=lambda x: self.get_level_priority(x[0]), reverse=True)
        for level, count in sorted_levels:
            level_color = self.get_level_color_name(level)
            stats_text += f"{level_color}({count}条) "

        stats_text += "\n预警类型统计: "
        for warning_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
            stats_text += f"{warning_type}({count}条) "

        fig.text(0.5, 0.02, stats_text, ha='center', fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))

        # 保存图片
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"warnings_table_{timestamp}.png"

        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"📸 表格图片已保存到: {filename}")
        return filename

    def print_statistics(self, warnings):
        """打印统计信息（保持原有功能）"""
        stats = self.get_statistics(warnings)

        if not stats:
            return

        print(f"\n📊 预警统计信息")
        print("=" * 50)
        print(f"总计: {stats['total']}条预警")

        print(f"\n按类型统计:")
        for warning_type, count in sorted(stats['by_type'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {warning_type}: {count}条")

        print(f"\n按等级统计:")
        for level, count in sorted(stats['by_level'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {level}: {count}条")

        print(f"\n按发布机构统计 (前5名):")
        for sender, count in sorted(stats['by_sender'].items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {sender}: {count}条")


def main():
    """主函数"""
    print("🚨 浙江省预警信息爬虫 - 单文件完整版")
    print("=" * 60)
    
    crawler = WeatherWarningCrawler()
    
    while True:
        print("\n请选择操作：")
        print("1. 获取当前预警信息")
        print("2. 获取预警信息并显示统计")
        print("3. 连续监控预警信息")
        print("4. 退出")
        
        choice = input("\n请输入选项 (1-4): ").strip()
        
        if choice == '1':
            raw_data = crawler.get_warning_data()
            if raw_data:
                warnings = crawler.parse_warning_data(raw_data)
                crawler.display_warnings(warnings)
                crawler.save_to_json(warnings)
        
        elif choice == '2':
            raw_data = crawler.get_warning_data()
            if raw_data:
                warnings = crawler.parse_warning_data(raw_data)
                crawler.print_table_format(warnings)
                crawler.save_to_json(warnings)

                # 询问是否生成图片
                if MATPLOTLIB_AVAILABLE:
                    generate_image = input("\n是否生成表格图片？(y/n，默认n): ").strip().lower()
                    if generate_image in ['y', 'yes', '是']:
                        crawler.generate_warning_image(warnings)
                else:
                    print("\n💡 提示：安装 matplotlib 后可生成表格图片")
                    print("   运行命令：pip install matplotlib")
        
        elif choice == '3':
            interval = input("请输入检查间隔（分钟，默认5）: ").strip()
            try:
                interval = int(interval) if interval else 5
            except ValueError:
                interval = 5
            
            print(f"🔄 开始连续监控，每{interval}分钟检查一次")
            previous_count = 0
            
            try:
                while True:
                    print(f"\n=== 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")
                    
                    raw_data = crawler.get_warning_data()
                    if raw_data:
                        warnings = crawler.parse_warning_data(raw_data)
                        current_count = len(warnings)
                        
                        if current_count != previous_count:
                            print(f"🚨 预警信息有变化！当前共{current_count}条预警（之前{previous_count}条）")
                            crawler.display_warnings(warnings)
                            
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            crawler.save_to_json(warnings, f"monitor_{timestamp}.json")
                            
                            previous_count = current_count
                        else:
                            print(f"预警信息无变化，当前共{current_count}条预警")
                    
                    print(f"等待{interval}分钟后进行下次检查...")
                    time.sleep(interval * 60)
                    
            except KeyboardInterrupt:
                print("\n监控已停止")
        
        elif choice == '4':
            print("退出程序")
            break
        
        else:
            print("无效选项，请重新选择")


if __name__ == "__main__":
    main()
