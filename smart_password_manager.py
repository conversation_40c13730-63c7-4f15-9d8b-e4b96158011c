#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能密码管理器
自动从官方JavaScript配置文件中获取最新密码
"""

import requests
import re
import json
import time
from datetime import datetime


class SmartPasswordManager:
    def __init__(self):
        self.base_url = "https://www.12379zj.cn"
        self.config_js_url = f"{self.base_url}/zjemw/resources/newClient/js/map/config/config.js"
        self.api_url = f"{self.base_url}/api/share"
        
        # 缓存配置
        self.cache_file = "password_cache.json"
        self.cache_duration = 3600  # 1小时缓存
    
    def get_password_from_config_js(self):
        """从官方config.js文件中提取密码"""
        try:
            print("正在从官方配置文件获取密码...")
            response = requests.get(self.config_js_url, timeout=10)
            response.raise_for_status()
            
            js_content = response.text
            
            # 提取apiPassword的值
            # 匹配模式: 'apiPassword':'密码内容'
            password_pattern = r"'apiPassword'\s*:\s*'([^']+)'"
            match = re.search(password_pattern, js_content)
            
            if match:
                password = match.group(1)
                print(f"✅ 成功获取密码 (长度: {len(password)})")
                return password
            else:
                print("❌ 未在配置文件中找到密码")
                return None
                
        except Exception as e:
            print(f"❌ 获取配置文件失败: {e}")
            return None
    
    def get_other_config_from_js(self):
        """从config.js获取其他配置信息"""
        try:
            response = requests.get(self.config_js_url, timeout=10)
            js_content = response.text
            
            config = {}
            
            # 提取各种配置
            patterns = {
                'apiUrl': r"'apiUrl'\s*:\s*'([^']+)'",
                'apiUserCode': r"'apiUserCode'\s*:\s*'([^']+)'",
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, js_content)
                if match:
                    config[key] = match.group(1)
            
            return config
            
        except Exception as e:
            print(f"获取其他配置失败: {e}")
            return {}
    
    def test_password(self, password):
        """测试密码是否有效"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://www.12379zj.cn',
            'Referer': 'https://www.12379zj.cn/map/index/toIndex'
        }
        
        data = {
            'userCode': 'ZJWechat',
            'password': password,
            'token': '1212232323',
            'identifier': 'iden11323232',
            'dataformat': 'JSON',
            'interfaceCode': 'A0001',
            'params': '{"isChildrens":true,"alarmType":2}'
        }
        
        try:
            response = requests.post(self.api_url, headers=headers, data=data, timeout=10)
            result = response.json()
            
            if result.get('returnCode') == '0':
                return True, result.get('total', 0)
            else:
                return False, result.get('returnMessage', '未知错误')
                
        except Exception as e:
            return False, str(e)
    
    def load_cache(self):
        """加载缓存的密码"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache = json.load(f)
            
            # 检查缓存是否过期
            cache_time = cache.get('timestamp', 0)
            if time.time() - cache_time < self.cache_duration:
                return cache.get('password')
            else:
                print("缓存已过期")
                return None
                
        except FileNotFoundError:
            print("无缓存文件")
            return None
        except Exception as e:
            print(f"读取缓存失败: {e}")
            return None
    
    def save_cache(self, password):
        """保存密码到缓存"""
        try:
            cache = {
                'password': password,
                'timestamp': time.time(),
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache, f, ensure_ascii=False, indent=2)
            
            print("✅ 密码已缓存")
            
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def get_valid_password(self):
        """获取有效的密码（优先使用缓存）"""
        print("=== 智能密码获取 ===")
        
        # 1. 尝试使用缓存
        cached_password = self.load_cache()
        if cached_password:
            print("使用缓存密码...")
            is_valid, result = self.test_password(cached_password)
            if is_valid:
                print(f"✅ 缓存密码有效，获取到 {result} 条预警信息")
                return cached_password
            else:
                print(f"❌ 缓存密码无效: {result}")
        
        # 2. 从官方配置文件获取新密码
        new_password = self.get_password_from_config_js()
        if new_password:
            print("测试新密码...")
            is_valid, result = self.test_password(new_password)
            if is_valid:
                print(f"✅ 新密码有效，获取到 {result} 条预警信息")
                self.save_cache(new_password)
                return new_password
            else:
                print(f"❌ 新密码无效: {result}")
        
        # 3. 使用备用密码（原始抓包的密码）
        fallback_password = "CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8="
        print("使用备用密码...")
        is_valid, result = self.test_password(fallback_password)
        if is_valid:
            print(f"✅ 备用密码有效，获取到 {result} 条预警信息")
            return fallback_password
        else:
            print(f"❌ 备用密码无效: {result}")
        
        print("❌ 所有密码都无效")
        return None
    
    def update_crawler_files(self, new_password):
        """更新爬虫文件中的密码"""
        files_to_update = [
            'weather_warning_crawler.py',
            'simple_crawler.py'
        ]
        
        # 旧密码（需要替换的）
        old_password = "CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8="
        
        updated_files = []
        
        for filename in files_to_update:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找并替换密码
                if old_password in content:
                    new_content = content.replace(old_password, new_password)
                    
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    updated_files.append(filename)
                    print(f"✅ 已更新文件: {filename}")
                else:
                    # 如果找不到旧密码，尝试用正则表达式替换
                    password_pattern = r"'password': '([^']+)'"
                    if re.search(password_pattern, content):
                        new_content = re.sub(password_pattern, f"'password': '{new_password}'", content)
                        
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(new_content)
                        
                        updated_files.append(filename)
                        print(f"✅ 已更新文件: {filename} (使用正则替换)")
                    else:
                        print(f"⚠️  文件 {filename} 中未找到密码字段")
                        
            except FileNotFoundError:
                print(f"⚠️  文件 {filename} 不存在")
            except Exception as e:
                print(f"❌ 更新文件 {filename} 失败: {e}")
        
        return updated_files
    
    def monitor_password_changes(self):
        """监控密码变化"""
        print("=== 密码变化监控 ===")
        
        current_password = self.get_valid_password()
        if not current_password:
            print("无法获取有效密码，监控终止")
            return
        
        print(f"开始监控密码变化... (当前密码长度: {len(current_password)})")
        
        check_count = 0
        while check_count < 5:  # 示例：检查5次
            time.sleep(60)  # 等待1分钟
            check_count += 1
            
            print(f"\n第{check_count}次检查...")
            new_password = self.get_password_from_config_js()
            
            if new_password and new_password != current_password:
                print("🚨 检测到密码变化！")
                print(f"旧密码长度: {len(current_password)}")
                print(f"新密码长度: {len(new_password)}")
                
                # 测试新密码
                is_valid, result = self.test_password(new_password)
                if is_valid:
                    print("✅ 新密码有效，正在更新文件...")
                    self.save_cache(new_password)
                    updated_files = self.update_crawler_files(new_password)
                    print(f"🎉 密码更新完成！已更新 {len(updated_files)} 个文件")
                    current_password = new_password
                else:
                    print(f"❌ 新密码无效: {result}")
            else:
                print("密码无变化")


def main():
    """主函数"""
    print("🧠 智能密码管理器")
    print("=" * 50)
    
    manager = SmartPasswordManager()
    
    while True:
        print("\n请选择操作：")
        print("1. 获取当前有效密码")
        print("2. 更新爬虫文件密码")
        print("3. 监控密码变化")
        print("4. 清除缓存")
        print("5. 退出")
        
        choice = input("\n请输入选项 (1-5): ").strip()
        
        if choice == '1':
            password = manager.get_valid_password()
            if password:
                print(f"\n当前有效密码: {password[:20]}...{password[-20:]}")
        
        elif choice == '2':
            password = manager.get_valid_password()
            if password:
                updated_files = manager.update_crawler_files(password)
                print(f"\n🎉 更新完成！已更新 {len(updated_files)} 个文件")
        
        elif choice == '3':
            manager.monitor_password_changes()
        
        elif choice == '4':
            try:
                import os
                os.remove(manager.cache_file)
                print("✅ 缓存已清除")
            except FileNotFoundError:
                print("⚠️  无缓存文件")
            except Exception as e:
                print(f"❌ 清除缓存失败: {e}")
        
        elif choice == '5':
            print("退出程序")
            break
        
        else:
            print("无效选项，请重新选择")


if __name__ == "__main__":
    main()
