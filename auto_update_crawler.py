#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动更新密码的爬虫
集成智能密码管理功能，无需手动更新密码
"""

import requests
import json
import re
import time
from datetime import datetime
from smart_password_manager import SmartPasswordManager


class AutoUpdateCrawler:
    def __init__(self):
        self.base_url = "https://www.12379zj.cn"
        self.api_url = f"{self.base_url}/api/share"
        self.password_manager = SmartPasswordManager()
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://www.12379zj.cn',
            'Referer': 'https://www.12379zj.cn/map/index/toIndex'
        }
    
    def get_warning_data(self, alarm_type=2, is_childrens=True, retry_count=3):
        """
        获取预警数据，自动处理密码更新
        
        Args:
            alarm_type (int): 预警类型
            is_childrens (bool): 是否包含子级预警
            retry_count (int): 重试次数
        
        Returns:
            dict: 预警数据
        """
        
        for attempt in range(retry_count):
            # 获取当前有效密码
            password = self.password_manager.get_valid_password()
            
            if not password:
                print(f"❌ 第{attempt+1}次尝试：无法获取有效密码")
                if attempt < retry_count - 1:
                    print("等待10秒后重试...")
                    time.sleep(10)
                continue
            
            # 构建请求数据
            data = {
                'userCode': 'ZJWechat',
                'password': password,
                'token': '1212232323',
                'identifier': 'iden11323232',
                'dataformat': 'JSON',
                'interfaceCode': 'A0001',
                'params': json.dumps({
                    "isChildrens": is_childrens,
                    "alarmType": alarm_type
                })
            }
            
            try:
                print(f"第{attempt+1}次尝试获取预警数据...")
                response = requests.post(self.api_url, headers=self.headers, data=data, timeout=30)
                response.raise_for_status()
                
                result = response.json()
                
                if result.get('returnCode') == '0':
                    print(f"✅ 成功获取数据，共 {result.get('total', 0)} 条预警信息")
                    return result
                else:
                    error_msg = result.get('returnMessage', '未知错误')
                    print(f"❌ API返回错误: {error_msg}")
                    
                    # 如果是密码错误，清除缓存并重试
                    if '密码' in error_msg or 'password' in error_msg.lower():
                        print("检测到密码错误，清除缓存...")
                        try:
                            import os
                            os.remove(self.password_manager.cache_file)
                        except:
                            pass
                        
                        if attempt < retry_count - 1:
                            print("等待5秒后重试...")
                            time.sleep(5)
                        continue
                    else:
                        return None
                        
            except requests.exceptions.RequestException as e:
                print(f"❌ 请求失败: {e}")
                if attempt < retry_count - 1:
                    print("等待5秒后重试...")
                    time.sleep(5)
                continue
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                if attempt < retry_count - 1:
                    print("等待5秒后重试...")
                    time.sleep(5)
                continue
        
        print("❌ 所有重试都失败了")
        return None
    
    def parse_warning_data(self, data):
        """解析预警数据"""
        if not data or 'data' not in data:
            return []
        
        warnings = []
        
        for item in data['data']:
            alert = item.get('alert', {})
            info = alert.get('info', {})
            area = info.get('area', {})
            
            warning_info = {
                'title': info.get('headline', ''),
                'sender': alert.get('sender', ''),
                'time': alert.get('sendTime', ''),
                'level': info.get('severity', ''),
                'type': info.get('disaName', ''),
                'area': area.get('areaDesc', ''),
                'expires': info.get('expires', ''),
                'description': info.get('description', ''),
                'identifier': alert.get('identifier', ''),
                'geocode': area.get('geocode', ''),
                'instruction': info.get('instruction', ''),
                'defences': info.get('defences', ''),
                'sender_name': info.get('senderName', ''),
                'urgency': info.get('urgency', ''),
                'certainty': info.get('certainty', '')
            }
            
            warnings.append(warning_info)
        
        return warnings
    
    def display_warnings(self, warnings):
        """显示预警信息"""
        if not warnings:
            print("当前没有预警信息")
            return
        
        print(f"\n浙江省预警信息 (共{len(warnings)}条)")
        print("=" * 80)
        
        for i, warning in enumerate(warnings, 1):
            print(f"\n{i}. {warning['title']}")
            print(f"   发布机构: {warning['sender']}")
            print(f"   发布时间: {warning['time']}")
            print(f"   预警等级: {warning['level']}")
            print(f"   灾害类型: {warning['type']}")
            print(f"   影响区域: {warning['area']}")
            print(f"   有效期至: {warning['expires']}")
            
            # 显示详细描述（截取前100字符）
            description = warning['description']
            if description:
                desc_short = description[:100] + "..." if len(description) > 100 else description
                print(f"   详细信息: {desc_short}")
            
            print("-" * 80)
    
    def save_to_json(self, warnings, filename=None):
        """保存为JSON格式"""
        if not warnings:
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"auto_warnings_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(warnings, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 数据已保存到: {filename}")
    
    def continuous_monitor(self, interval_minutes=5, max_runs=None):
        """
        连续监控预警信息
        
        Args:
            interval_minutes (int): 检查间隔（分钟）
            max_runs (int): 最大运行次数，None表示无限运行
        """
        print(f"🔄 开始连续监控，每{interval_minutes}分钟检查一次")
        
        run_count = 0
        previous_count = 0
        
        while True:
            run_count += 1
            print(f"\n=== 第{run_count}次检查 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ===")
            
            # 获取预警数据
            raw_data = self.get_warning_data()
            
            if raw_data:
                warnings = self.parse_warning_data(raw_data)
                current_count = len(warnings)
                
                if current_count != previous_count:
                    print(f"🚨 预警信息有变化！当前共{current_count}条预警（之前{previous_count}条）")
                    self.display_warnings(warnings)
                    
                    # 保存数据
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    self.save_to_json(warnings, f"monitor_{timestamp}.json")
                    
                    previous_count = current_count
                else:
                    print(f"预警信息无变化，当前共{current_count}条预警")
            else:
                print("❌ 获取预警数据失败")
            
            # 检查是否达到最大运行次数
            if max_runs and run_count >= max_runs:
                print(f"已达到最大运行次数 {max_runs}，停止监控")
                break
            
            # 等待下次检查
            print(f"等待{interval_minutes}分钟后进行下次检查...")
            time.sleep(interval_minutes * 60)
    
    def get_statistics(self, warnings):
        """获取预警统计信息"""
        if not warnings:
            return {}
        
        stats = {
            'total': len(warnings),
            'by_type': {},
            'by_level': {},
            'by_sender': {}
        }
        
        for warning in warnings:
            # 按类型统计
            warning_type = warning['type']
            stats['by_type'][warning_type] = stats['by_type'].get(warning_type, 0) + 1
            
            # 按等级统计
            level = warning['level']
            stats['by_level'][level] = stats['by_level'].get(level, 0) + 1
            
            # 按发布机构统计
            sender = warning['sender']
            stats['by_sender'][sender] = stats['by_sender'].get(sender, 0) + 1
        
        return stats
    
    def print_statistics(self, warnings):
        """打印统计信息"""
        stats = self.get_statistics(warnings)
        
        if not stats:
            print("无统计数据")
            return
        
        print(f"\n📊 预警统计信息")
        print("=" * 50)
        print(f"总计: {stats['total']}条预警")
        
        print(f"\n按类型统计:")
        for warning_type, count in sorted(stats['by_type'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {warning_type}: {count}条")
        
        print(f"\n按等级统计:")
        for level, count in sorted(stats['by_level'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {level}: {count}条")
        
        print(f"\n按发布机构统计 (前5名):")
        for sender, count in sorted(stats['by_sender'].items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {sender}: {count}条")


def main():
    """主函数"""
    print("🤖 自动更新密码的浙江省预警信息爬虫")
    print("=" * 60)
    
    crawler = AutoUpdateCrawler()
    
    while True:
        print("\n请选择操作：")
        print("1. 获取当前预警信息")
        print("2. 获取预警信息并显示统计")
        print("3. 连续监控预警信息")
        print("4. 测试密码管理器")
        print("5. 退出")
        
        choice = input("\n请输入选项 (1-5): ").strip()
        
        if choice == '1':
            raw_data = crawler.get_warning_data()
            if raw_data:
                warnings = crawler.parse_warning_data(raw_data)
                crawler.display_warnings(warnings)
                crawler.save_to_json(warnings)
        
        elif choice == '2':
            raw_data = crawler.get_warning_data()
            if raw_data:
                warnings = crawler.parse_warning_data(raw_data)
                crawler.display_warnings(warnings)
                crawler.print_statistics(warnings)
                crawler.save_to_json(warnings)
        
        elif choice == '3':
            interval = input("请输入检查间隔（分钟，默认5）: ").strip()
            try:
                interval = int(interval) if interval else 5
            except ValueError:
                interval = 5
            
            max_runs = input("请输入最大运行次数（默认无限）: ").strip()
            try:
                max_runs = int(max_runs) if max_runs else None
            except ValueError:
                max_runs = None
            
            crawler.continuous_monitor(interval, max_runs)
        
        elif choice == '4':
            password = crawler.password_manager.get_valid_password()
            if password:
                print(f"✅ 密码管理器工作正常")
                print(f"当前密码长度: {len(password)}")
            else:
                print("❌ 密码管理器无法获取有效密码")
        
        elif choice == '5':
            print("退出程序")
            break
        
        else:
            print("无效选项，请重新选择")


if __name__ == "__main__":
    main()
