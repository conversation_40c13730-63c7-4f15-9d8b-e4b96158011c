# 浙江省预警信息爬虫项目

## 项目概述

基于您提供的4个抓包文件，我成功开发了一个浙江省12379预警信息发布平台的爬虫系统。该系统可以实时获取浙江省各地的气象预警信息，包括雷电、雷暴大风、地质灾害等各类预警。

## 抓包文件分析结果

通过分析您提供的4个抓包文件，我发现了以下关键信息：

1. **1_Full.txt**: 神策数据统计请求（用户行为追踪）
2. **2_Full.txt**: 主页面HTML内容获取
3. **3_Full.txt**: 页脚内容获取
4. **4_Full.txt**: **核心API接口** - POST请求到 `/api/share` 获取预警数据

### 关键API信息
- **URL**: `https://www.12379zj.cn/api/share`
- **方法**: POST
- **认证**: 使用固定的userCode和password
- **数据格式**: JSON
- **接口代码**: A0001

## 开发的文件说明

### 核心文件

1. **weather_warning_crawler.py** - 完整功能爬虫
   - 面向对象设计
   - 完整的数据解析和处理
   - 支持CSV和JSON导出
   - 包含数据统计分析功能

2. **simple_crawler.py** - 简化版爬虫（推荐日常使用）
   - 代码简洁易懂
   - 快速获取和显示预警信息
   - 适合快速查看当前预警状态

3. **example_usage.py** - 使用示例
   - 基本使用示例
   - 数据分析示例
   - 地区筛选示例
   - 连续监控示例

### 配置文件

4. **requirements.txt** - 依赖包
   ```
   requests>=2.25.1
   pandas>=1.3.0
   ```

5. **README.md** - 详细使用说明

## 功能特点

### ✅ 已实现功能

- 🚨 **实时预警获取**: 获取浙江省各地实时预警信息
- 📊 **多格式导出**: 支持CSV和JSON格式数据导出
- 🔍 **数据筛选**: 支持按地区、预警等级、灾害类型筛选
- 📈 **统计分析**: 提供预警类型、等级、发布机构统计
- ⏰ **连续监控**: 支持定时监控预警信息变化
- 🛡️ **稳定可靠**: 模拟真实浏览器请求，通过率高

### 📋 数据字段

爬取的预警数据包含以下完整字段：
- 预警标识符、发布机构、发布时间
- 预警标题、等级、灾害类型
- 影响区域、地理编码
- 生效时间、过期时间
- 详细描述、防御指南
- 发布人员信息等

## 测试结果

### 成功获取数据示例
```
浙江省预警信息 (共10条)
================================================================================

1. 衢江区气象台发布雷电黄色预警[Ⅲ级/较重]
   发布机构: 衢江区气象台
   发布时间: 2025-07-14 19:36:00+08:00
   预警等级: Yellow
   灾害类型: 雷电
   影响区域: 莲花镇,高家镇,樟潭街道,杜泽镇,双桥乡,周家乡,全旺镇,大洲镇...
   有效期至: 2025-07-15 19:36:00+08:00

2. 台州市黄岩区气象台发布雷电黄色预警[Ⅲ级/较重]
   ...
```

### 数据统计示例
```
--- 灾害类型统计 ---
雷电: 9条
雷暴大风: 1条

--- 预警等级统计 ---
Yellow: 10条

--- 发布机构统计 (前5名) ---
临海市气象台: 2条
衢江区气象台: 1条
台州市黄岩区气象台: 1条
...
```

## 使用方法

### 快速开始
```bash
# 安装依赖
pip install -r requirements.txt

# 快速获取预警信息
python simple_crawler.py

# 使用完整功能版本
python weather_warning_crawler.py

# 查看使用示例
python example_usage.py
```

### 代码集成示例
```python
from weather_warning_crawler import WeatherWarningCrawler

# 创建爬虫实例
crawler = WeatherWarningCrawler()

# 获取预警数据
raw_data = crawler.get_warning_data()
warnings = crawler.parse_warning_data(raw_data)

# 处理数据
for warning in warnings:
    print(f"预警: {warning['headline']}")
    print(f"区域: {warning['area_desc']}")
```

## 技术特点

1. **基于真实抓包**: 完全基于您提供的抓包文件开发，确保兼容性
2. **请求头完整**: 包含完整的浏览器请求头，避免被识别为爬虫
3. **错误处理**: 完善的异常处理和错误提示
4. **编码处理**: 正确处理中文编码问题
5. **数据结构化**: 将原始JSON数据转换为结构化格式

## 注意事项

1. **请求频率**: 建议控制请求频率，避免对服务器造成压力
2. **数据时效性**: 预警信息具有时效性，请及时处理
3. **网络稳定性**: 确保网络连接稳定
4. **合规使用**: 仅用于学习研究目的，遵守相关法律法规

## 可能的扩展功能

1. **地图可视化**: 结合地图API显示预警分布
2. **消息推送**: 集成微信、邮件等推送功能
3. **历史数据**: 建立数据库存储历史预警信息
4. **预警分析**: 基于历史数据进行预警趋势分析
5. **多地区支持**: 扩展到其他省份的预警系统

## 项目文件清单

```
├── weather_warning_crawler.py    # 完整功能爬虫
├── simple_crawler.py            # 简化版爬虫
├── example_usage.py             # 使用示例
├── requirements.txt             # 依赖包
├── README.md                   # 使用说明
├── 项目说明.md                  # 项目总结（本文件）
└── 生成的数据文件/
    ├── weather_warnings_*.csv   # CSV格式数据
    ├── weather_warnings_*.json  # 完整JSON数据
    └── warnings_simple_*.json   # 简化JSON数据
```

## 总结

基于您提供的抓包文件，我成功开发了一个功能完整、稳定可靠的浙江省预警信息爬虫系统。该系统不仅能够实时获取预警数据，还提供了丰富的数据处理和分析功能，满足不同场景的使用需求。

项目代码结构清晰，注释详细，易于理解和二次开发。同时提供了简化版本，方便日常快速使用。

如果您需要任何功能调整或有其他需求，请随时告知！
