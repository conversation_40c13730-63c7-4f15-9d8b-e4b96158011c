#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浙江省预警信息简化爬虫
快速获取和显示预警信息
"""

import requests
import json
from datetime import datetime


def get_weather_warnings():
    """获取浙江省预警信息"""
    
    url = "https://www.12379zj.cn/api/share"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Origin': 'https://www.12379zj.cn',
        'Referer': 'https://www.12379zj.cn/map/index/toIndex'
    }
    
    data = {
        'userCode': 'ZJWechat',
        'password': 'CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8=',
        'token': '1212232323',
        'identifier': 'iden11323232',
        'dataformat': 'JSON',
        'interfaceCode': 'A0001',
        'params': '{"isChildrens":true,"alarmType":2}'
    }
    
    try:
        response = requests.post(url, headers=headers, data=data, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        
        if result.get('returnCode') == '0':
            return result.get('data', [])
        else:
            print(f"API错误: {result.get('returnMessage', '未知错误')}")
            return []
            
    except Exception as e:
        print(f"请求失败: {e}")
        return []


def display_warnings(warnings_data):
    """显示预警信息"""
    
    if not warnings_data:
        print("当前没有预警信息")
        return
    
    print(f"\n浙江省预警信息 (共{len(warnings_data)}条)")
    print("=" * 80)
    
    for i, item in enumerate(warnings_data, 1):
        alert = item.get('alert', {})
        info = alert.get('info', {})
        area = info.get('area', {})
        
        print(f"\n{i}. {info.get('headline', '未知预警')}")
        print(f"   发布机构: {alert.get('sender', '未知')}")
        print(f"   发布时间: {alert.get('sendTime', '未知')}")
        print(f"   预警等级: {info.get('severity', '未知')}")
        print(f"   灾害类型: {info.get('disaName', '未知')}")
        print(f"   影响区域: {area.get('areaDesc', '未知')}")
        print(f"   有效期至: {info.get('expires', '未知')}")
        
        # 显示详细描述（截取前100字符）
        description = info.get('description', '')
        if description:
            desc_short = description[:100] + "..." if len(description) > 100 else description
            print(f"   详细信息: {desc_short}")
        
        print("-" * 80)


def save_simple_json(warnings_data, filename=None):
    """保存为简化的JSON格式"""
    
    if not warnings_data:
        return
    
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"warnings_simple_{timestamp}.json"
    
    simplified_data = []
    
    for item in warnings_data:
        alert = item.get('alert', {})
        info = alert.get('info', {})
        area = info.get('area', {})
        
        simple_warning = {
            'title': info.get('headline', ''),
            'sender': alert.get('sender', ''),
            'time': alert.get('sendTime', ''),
            'level': info.get('severity', ''),
            'type': info.get('disaName', ''),
            'area': area.get('areaDesc', ''),
            'expires': info.get('expires', ''),
            'description': info.get('description', '')
        }
        
        simplified_data.append(simple_warning)
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(simplified_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n数据已保存到: {filename}")


def main():
    """主函数"""
    print("正在获取浙江省预警信息...")
    
    # 获取预警数据
    warnings = get_weather_warnings()
    
    # 显示预警信息
    display_warnings(warnings)
    
    # 保存数据
    if warnings:
        save_simple_json(warnings)
    
    print(f"\n获取完成！当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
