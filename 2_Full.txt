GET https://www.12379zj.cn/map/index/toIndex HTTP/1.1
Host: www.12379zj.cn
Connection: keep-alive
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: iframe
Referer: https://www.12379zj.cn/
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: Hm_lvt_d2d4f79552e17613f35f20fdc41b77d6=**********; HMACCOUNT=373D29C0AA168445; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219807c1c62f564-0b5bdd71fd0cda8-********-1238400-19807c1c630c4a%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk4MDdjMWM2MmY1NjQtMGI1YmRkNzFmZDBjZGE4LTI2MDExMTUxLTEyMzg0MDAtMTk4MDdjMWM2MzBjNGEifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219807c1c62f564-0b5bdd71fd0cda8-********-1238400-19807c1c630c4a%22%7D; sajssdk_2015_cross_new_user=1; Hm_lpvt_d2d4f79552e17613f35f20fdc41b77d6=**********; sensorsdata2015jssdksession=%7B%22session_id%22%3A%2219807c1c64023800850a0b18cad56********123840019807c1c6411467%22%2C%22first_session_time%22%3A**********480%2C%22latest_session_time%22%3A1752477331759%7D


HTTP/1.1 200
Date: Mon, 14 Jul 2025 07:15:31 GMT
Content-Type: text/html;charset=utf-8
Connection: keep-alive
Vary: Accept-Encoding
Access-Control-Allow-Origin: *
Set-Cookie: JSESSIONID=EBAF113BC0C9CD31A98CDEA28B5BA696; Path=/zjemw; HttpOnly
Content-Language: zh-CN
X-Frame-Options: SAMEORIGIN
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true
Access-Control-Allow-Methods: *
Access-Control-Allow-Headers: *
Content-Length: 7335




<!DOCTYPE html>
<html>
<head>
    <base href="https://www.12379zj.cn:443/zjemw/"/>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta content="width=device-width" name="viewport"/>
    <title></title>
    <link rel="stylesheet" href="resources/newClient/css/reset.css">
            <link rel="stylesheet" href="resources/newClient/css/index.css">
    <link rel="stylesheet" href="resources/newClient/js/map/js/gis/lib/v4.2.0/ol.css" />
    <link rel="stylesheet" href="resources/newClient/js/map/css/popup.css" />
    <style>
        #map{
            width: 100%;
            height: auto;
            overflow: hidden;
        }
        .layer-swich{
            display: block;
            position: absolute;
            bottom: 70px;
            left: 50px;
            color: #000502;
        }
        .layer-swich ul li{
            float: left;
            margin: 2px;
            width: 80px;
            height: 40px;
            text-align: center;
            border-radius: 10px;
            border: 1px #999999 solid;
            font-size: 20px;
        // background: blue;
            cursor: pointer;
        }
        .layer-swich ul li.selectedLi{
            color: #FFF6E5;
            background: deepskyblue;
        }
        .layer-swich ul li span{
            line-height: 38px;
        }
        .ico_list {
            position: absolute;
            height: 20px;
            width: 100%;
            z-index: 10;
            top: 100px;
            left: 0;
        }

        @media screen and (max-width:1366px){
            .layer-swich{
                bottom: 60px;
            }
        }

    </style>
</head>
<body>
        <div class="content" style="position: fixed;height: 100%;" >
            <div class="warning_box">
                <div class="list_btn"><img src="resources/newClient/image/list_btn.png" alt=""></div>
                <div class="list_content" style="height: 105px;">
                    <div class="list-move">
                        <div class="list_colse"></div>
                        <p>生效预警</p>
                        <div class="list_con"><span></span><a href="javascript:;" onclick="redirectPage()">列表&gt;&gt;</a></div>
                        <div class="warning_content"></div>
                    </div>
                    <div id="hideshow">
                        <div class="ico_list" style="top: 80px;"><div id="online_qq_tab_ico"><a id="floatShow_ico" style="display:block" href="javascript:void(0);"></a><a id="floatHide_ico" style="display:none" href="javascript:void(0);"></a></div></div>
                    </div>
                </div>
            </div>
            <div class="legend_box">
                <div class="legend_title">
                    <span>图例</span>
                    <span></span>
                </div>
                <div class="legend_main">
                    <div class="leg_main1">
                        <i></i>
                        <span>红色预警（I级）</span>
                    </div>
                    <div class="leg_main1">
                        <i></i>
                        <span>橙色预警（II级）</span>
                    </div>
                    <div class="leg_main1">
                        <i></i>
                        <span>黄色预警（III级）</span>
                    </div>
                    <div class="leg_main1">
                        <i></i>
                        <span>蓝色预警（IV级）</span>
                    </div>
                    <div class="leg_main1">
                        <i></i>
                        <span>提示信息</span>
                    </div>
                    <div class="leg_main1">
                        <i></i>
                        <span>24时内灾情</span>
                    </div>
                </div>
            </div>
            <div class="list_btn2"><img src="resources/newClient/image/list_btn.png" alt=""></div>
            <div id="map" style="height:100%;"></div>

            <div class="layer-swich">
                <ul>
                    <li data-layer = "warningTianseLayer" class="selectedLi"><span>颜色</span></li>
                    <li data-layer = "warningImgLayer" class=""><span>图标</span></li>
                </ul>

            </div>
            

        </div>
        
        <div class="foot" style="position: absolute;
    bottom: 0px;
    width: 100%;">
            <iframe id="footIframe" frameborder="0" src="foot/toFoot"  style="position:relative;z-index:99;width: 100% ;height: 68px !important;display: block;"></iframe>
        </div>

<!--地图气泡框开始-->
<div id="popup-w" class="ol-popup-w" style="display: none;">
    <a href="#" id="popup-closer-w" class="ol-popup-closer-w"></a>
    <!--<div id="popup-content-w" style="width: 400px;height: 300px;">-->

    <div id="popup-content-w" >

        <div class="summary">
            <dl>
                <dt>
                    <span class="map_red" style="background:#edd405"></span>
                    <a href="/html/new2018/alarmcontent.shtml?file=14092641600000_20190911195854.html" title="静乐县气象局发布地质灾害气象风险黄色预警[Ⅲ级/较重]">
                        <h2 style="color:#edd405">静乐县气象局发布地质灾害气象风险黄色...</h2></a>
                    <span class="map_time">2019-09-11 19:55:28</span>
                    <div class="clear"></div>
                </dt>
                <dd>
                    <p>[静乐县自然资源局][地质灾害气象风险预警]静乐县自然资源局和静乐县气象局2019年9月11日19时58分联合发布地质灾害气象风险黄色预警：预计未来24小时，我县部分区域地质灾害气象风险级别为3级（黄色），发生地质灾害风险较大，请各乡镇及地质灾害防治成员单位根据职责分工组织地质灾害隐患点24小时巡查。预警起始时间:2019年9月11日20时。</p>
                </dd>
            </dl>
        </div>
        <div class="map_title">
            <ul></ul>
        </div>


    </div>
</div>
<!--地图气泡框结束-->

<script>
    var basePath = 'https://www.12379zj.cn:443/zjemw/';
    var _hmt = _hmt || [];
    (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?d2d4f79552e17613f35f20fdc41b77d6";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
</script>
<script src="resources/newClient/js/jquery-3.3.1.min.js"></script>
<script src="resources/newClient/js/map/js/gis/lib/v4.2.0/ol.js"></script>
<script src="resources/newClient/js/map/js/gis/common/gisCommon.js"></script>
<script src="resources/newClient/js/map/js/gis/lib/ssd.min.js"></script>
<script src="resources/newClient/js/map/config/config.js"></script>
<script src="resources/newClient/js/map/js/init.js"></script>
<script src="resources/newClient/js/map/js/warning.js"></script>
<script src="sw.webjs.sdk/autotrack.js"></script>
</body>
</html>