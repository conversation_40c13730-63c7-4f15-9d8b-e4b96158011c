#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浙江省预警信息爬虫
基于抓包文件分析的API接口爬取预警数据
"""

import requests
import json
import time
from datetime import datetime
from urllib.parse import unquote
import pandas as pd


class WeatherWarningCrawler:
    def __init__(self):
        self.base_url = "https://www.12379zj.cn"
        self.api_url = f"{self.base_url}/api/share"
        
        # 基于抓包文件的请求头
        self.headers = {
            'Host': 'www.12379zj.cn',
            'Connection': 'keep-alive',
            'sec-ch-ua-platform': '"Windows"',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'sec-ch-ua-mobile': '?0',
            'Origin': 'https://www.12379zj.cn',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://www.12379zj.cn/map/index/toIndex',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9'
        }
        
        # 会话对象，用于保持cookie
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def get_warning_data(self, alarm_type=2, is_childrens=True):
        """
        获取预警数据
        
        Args:
            alarm_type (int): 预警类型，默认为2
            is_childrens (bool): 是否包含子级预警，默认为True
        
        Returns:
            dict: 预警数据
        """
        # 基于抓包文件的POST数据 (密码已URL解码)
        post_data = {
            'userCode': 'ZJWechat',
            'password': 'CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8=',
            'token': '1212232323',
            'identifier': 'iden11323232',
            'dataformat': 'JSON',
            'interfaceCode': 'A0001',
            'params': json.dumps({
                "isChildrens": is_childrens,
                "alarmType": alarm_type
            })
        }
        
        try:
            response = self.session.post(self.api_url, data=post_data, timeout=30)
            response.raise_for_status()
            
            # 解析JSON响应
            data = response.json()
            
            if data.get('returnCode') == '0':
                print(f"成功获取数据，共 {data.get('total', 0)} 条预警信息")
                return data
            else:
                print(f"API返回错误: {data.get('returnMessage', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return None
    
    def parse_warning_data(self, data):
        """
        解析预警数据
        
        Args:
            data (dict): 原始预警数据
        
        Returns:
            list: 解析后的预警信息列表
        """
        if not data or 'data' not in data:
            return []
        
        warnings = []
        
        for item in data['data']:
            alert = item.get('alert', {})
            info = alert.get('info', {})
            area = info.get('area', {})
            
            warning_info = {
                'identifier': alert.get('identifier', ''),
                'sender': alert.get('sender', ''),
                'send_time': alert.get('sendTime', ''),
                'status': alert.get('status', ''),
                'headline': info.get('headline', ''),
                'severity': info.get('severity', ''),
                'disaster_name': info.get('disaName', ''),
                'event_type': info.get('eventType', ''),
                'effective_time': info.get('effective', ''),
                'expires_time': info.get('expires', ''),
                'area_desc': area.get('areaDesc', ''),
                'geocode': area.get('geocode', ''),
                'description': info.get('description', ''),
                'instruction': info.get('instruction', ''),
                'defences': info.get('defences', ''),
                'sender_name': info.get('senderName', ''),
                'urgency': info.get('urgency', ''),
                'certainty': info.get('certainty', '')
            }
            
            warnings.append(warning_info)
        
        return warnings
    
    def save_to_csv(self, warnings, filename=None):
        """
        保存预警数据到CSV文件
        
        Args:
            warnings (list): 预警信息列表
            filename (str): 文件名，默认使用时间戳
        """
        if not warnings:
            print("没有数据可保存")
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"weather_warnings_{timestamp}.csv"
        
        df = pd.DataFrame(warnings)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filename}")
    
    def save_to_json(self, warnings, filename=None):
        """
        保存预警数据到JSON文件
        
        Args:
            warnings (list): 预警信息列表
            filename (str): 文件名，默认使用时间戳
        """
        if not warnings:
            print("没有数据可保存")
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"weather_warnings_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(warnings, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {filename}")
    
    def print_warnings_summary(self, warnings):
        """
        打印预警信息摘要
        
        Args:
            warnings (list): 预警信息列表
        """
        if not warnings:
            print("没有预警信息")
            return
        
        print(f"\n=== 预警信息摘要 (共{len(warnings)}条) ===")
        print("-" * 80)
        
        for i, warning in enumerate(warnings, 1):
            print(f"{i}. {warning['headline']}")
            print(f"   发布机构: {warning['sender']}")
            print(f"   发布时间: {warning['send_time']}")
            print(f"   预警等级: {warning['severity']}")
            print(f"   影响区域: {warning['area_desc']}")
            print(f"   有效期至: {warning['expires_time']}")
            print("-" * 80)


def main():
    """主函数"""
    print("浙江省预警信息爬虫启动...")
    
    # 创建爬虫实例
    crawler = WeatherWarningCrawler()
    
    # 获取预警数据
    print("正在获取预警数据...")
    raw_data = crawler.get_warning_data()
    
    if raw_data:
        # 解析数据
        warnings = crawler.parse_warning_data(raw_data)
        
        # 打印摘要
        crawler.print_warnings_summary(warnings)
        
        # 保存数据
        if warnings:
            crawler.save_to_csv(warnings)
            crawler.save_to_json(warnings)
        
        print(f"\n爬取完成！共获取 {len(warnings)} 条预警信息")
    else:
        print("未能获取到预警数据")


if __name__ == "__main__":
    main()
