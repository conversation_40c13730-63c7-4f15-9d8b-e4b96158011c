#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
存储API相关配置信息
"""

# API配置
API_CONFIG = {
    'base_url': 'https://www.12379zj.cn',
    'api_endpoint': '/api/share',
    'user_code': 'ZJWechat',
    'password': 'CifcIqVPix/S4rwY6Y91ZIeAS9sw7DPQstphN8v5iqYQ8dfv6dolHImGoUih9vPr0WPyb1wTHCCcghKnzmIlIQDeytofz5ixJx9HlBYluDN9K3tf43gCNjSS4VEqpvk6RZ+DNvsjTcCNaEn9/KNR+foUkI51FBqjV96xxbcNGC8=',
    'token': '1212232323',
    'identifier': 'iden11323232',
    'dataformat': 'JSON',
    'interface_code': 'A0001'
}

# 请求头配置
HEADERS = {
    'Host': 'www.12379zj.cn',
    'Connection': 'keep-alive',
    'sec-ch-ua-platform': '"Windows"',
    'X-Requested-With': 'XMLHttpRequest',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'sec-ch-ua-mobile': '?0',
    'Origin': 'https://www.12379zj.cn',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Dest': 'empty',
    'Referer': 'https://www.12379zj.cn/map/index/toIndex',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'Accept-Language': 'zh-CN,zh;q=0.9'
}

# 默认参数
DEFAULT_PARAMS = {
    'is_childrens': True,
    'alarm_type': 2
}

# 更新密码的函数
def update_password(new_password):
    """更新配置中的密码"""
    global API_CONFIG
    API_CONFIG['password'] = new_password
    
    # 同时更新到文件中
    import os
    config_file = __file__
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换密码行
        import re
        pattern = r"'password': '([^']+)'"
        new_content = re.sub(pattern, f"'password': '{new_password}'", content)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 配置文件已更新")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")
        return False
