# 浙江省预警信息爬虫 - 单文件完整版

基于抓包文件分析开发的浙江省预警信息爬虫，可以获取浙江省12379预警信息发布平台的实时预警数据。

**🎉 现在只需要一个文件就能完整运行！**

## 功能特点

- 🚨 获取实时预警信息（雷电、雷暴大风、地质灾害等）
- 🤖 **自动密码管理** - 永远不用担心密码变化问题
- 📊 数据统计分析功能
- ⏰ 连续监控模式
- 💾 自动保存JSON格式数据
- 🔄 智能缓存机制
- 🛡️ 自动重试和错误处理
- 📱 交互式菜单操作

## 安装依赖

```bash
pip install requests
```

## 快速开始

### 一键运行

```bash
python all_in_one_crawler.py
```

### 功能菜单

运行后会显示交互式菜单：

```
请选择操作：
1. 获取当前预警信息
2. 获取预警信息并显示统计
3. 连续监控预警信息
4. 退出
```

## 核心优势

### 🔐 自动密码管理
- 自动从官方配置文件获取最新密码
- 智能缓存机制（1小时缓存）
- 密码变化时自动更新
- 多重保障：缓存 → 官方源 → 备用密码

### 📊 数据字段说明

爬取的预警数据包含以下主要字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| title | 预警标题 | 象山县气象台发布雷电黄色预警[Ⅲ级/较重] |
| sender | 发布机构 | 象山县气象台 |
| time | 发布时间 | 2025-07-14 15:05:00+08:00 |
| level | 预警等级 | Yellow |
| type | 灾害类型 | 雷电 |
| area | 影响区域 | 象山县 |
| expires | 过期时间 | 2025-07-15 15:05:00+08:00 |
| description | 详细描述 | 预警详细内容 |
| identifier | 预警标识符 | 33022541600000_20250714150948 |
| instruction | 防御指南 | 防御措施建议 |

## 使用示例

### 1. 基本使用
```bash
python all_in_one_crawler.py
# 选择选项1：获取当前预警信息
```

### 2. 统计分析
```bash
python all_in_one_crawler.py
# 选择选项2：获取预警信息并显示统计
```

### 3. 连续监控
```bash
python all_in_one_crawler.py
# 选择选项3：连续监控预警信息
# 可以自定义监控间隔（分钟）
```

### 4. 程序化调用

如果需要在其他Python程序中使用：

```python
from all_in_one_crawler import WeatherWarningCrawler

# 创建爬虫实例
crawler = WeatherWarningCrawler()

# 获取预警数据
raw_data = crawler.get_warning_data()
if raw_data:
    warnings = crawler.parse_warning_data(raw_data)
    print(f"获取到 {len(warnings)} 条预警信息")

    # 显示预警信息
    crawler.display_warnings(warnings)

    # 保存数据
    crawler.save_to_json(warnings)

    # 获取统计信息
    crawler.print_statistics(warnings)
```

## 文件说明

- `all_in_one_crawler.py` - **唯一需要的核心文件**
- `requirements.txt` - 依赖包列表（只需要 requests）
- `README.md` - 使用说明
- `password_cache.json` - 自动生成的密码缓存文件
- `warnings_*.json` - 自动生成的预警数据文件

## 技术特点

### 🔐 智能密码管理
- 自动从官方JavaScript配置文件获取密码
- 密码来源：`https://www.12379zj.cn/zjemw/resources/newClient/js/map/config/config.js`
- 三重保障机制：缓存密码 → 官方密码 → 备用密码
- 密码变化时自动清除缓存并重新获取

### 🛡️ 稳定性保障
- 自动重试机制（默认3次）
- 智能错误处理
- 网络异常自动恢复
- 密码错误自动更新

## 注意事项

1. **请求频率**: 建议控制请求频率，避免对服务器造成过大压力
2. **数据时效性**: 预警信息具有时效性，请及时处理获取的数据
3. **网络环境**: 确保网络连接稳定
4. **合规使用**: 请遵守相关法律法规，仅用于学习和研究目的

## 常见问题

### Q: 密码变了怎么办？
A: **完全不用担心！** 程序会自动从官方配置文件获取最新密码，无需手动处理。

### Q: 为什么有时候获取不到数据？
A: 程序有自动重试机制，如果多次重试仍失败，可能是：
- 网络连接问题
- 服务器临时不可用

### Q: 如何停止连续监控？
A: 在连续监控模式下，按 `Ctrl+C` 可以停止监控。

### Q: 数据保存在哪里？
A: 数据自动保存为JSON格式，文件名包含时间戳，如 `warnings_20250714_204911.json`

## 更新日志

- **v2.0.0 (2025-07-14) - 单文件完整版**
  - 🎉 整合所有功能到单个文件
  - 🔐 集成智能密码管理
  - 🤖 自动密码更新机制
  - 📊 内置统计分析功能
  - ⏰ 连续监控模式
  - 🛡️ 完善的错误处理

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
