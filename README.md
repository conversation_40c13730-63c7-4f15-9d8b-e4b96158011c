# 浙江省预警信息爬虫

基于抓包文件分析开发的浙江省预警信息爬虫，可以获取浙江省12379预警信息发布平台的实时预警数据。

## 功能特点

- 🚨 获取实时预警信息（雷电、雷暴大风、地质灾害等）
- 📊 支持数据导出（CSV、JSON格式）
- 🔍 支持按地区、类型筛选预警信息
- 📈 提供数据统计分析功能
- ⏰ 支持连续监控模式
- 🛡️ 模拟真实浏览器请求，稳定可靠

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 基本使用

```python
from weather_warning_crawler import WeatherWarningCrawler

# 创建爬虫实例
crawler = WeatherWarningCrawler()

# 获取预警数据
raw_data = crawler.get_warning_data()

if raw_data:
    # 解析数据
    warnings = crawler.parse_warning_data(raw_data)
    
    # 打印摘要
    crawler.print_warnings_summary(warnings)
    
    # 保存数据
    crawler.save_to_csv(warnings)
    crawler.save_to_json(warnings)
```

### 运行示例

```bash
# 快速获取预警信息（推荐）
python simple_crawler.py

# 完整功能版本
python weather_warning_crawler.py

# 查看更多使用示例
python example_usage.py
```

## API参数说明

### get_warning_data() 方法参数

- `alarm_type` (int): 预警类型，默认为2
- `is_childrens` (bool): 是否包含子级预警，默认为True

## 数据字段说明

爬取的预警数据包含以下字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| identifier | 预警标识符 | 33022541600000_20250714150948 |
| sender | 发布机构 | 象山县气象台 |
| send_time | 发布时间 | 2025-07-14 15:05:00+08:00 |
| status | 状态 | Actual |
| headline | 预警标题 | 象山县气象台发布雷电黄色预警[Ⅲ级/较重] |
| severity | 预警等级 | Yellow |
| disaster_name | 灾害名称 | 雷电 |
| event_type | 事件类型 | 11B14 |
| effective_time | 生效时间 | 2025-07-14 15:05:00+08:00 |
| expires_time | 过期时间 | 2025-07-15 15:05:00+08:00 |
| area_desc | 影响区域 | 象山县 |
| geocode | 地理编码 | 330225000000 |
| description | 详细描述 | 预警详细内容 |
| instruction | 防御指南 | 防御措施建议 |
| defences | 防御措施 | 具体防御措施 |
| sender_name | 发布人员 | 象山气象采集员 |
| urgency | 紧急程度 | Unknown |
| certainty | 确定性 | Unknown |

## 使用示例

### 1. 按地区筛选预警

```python
# 筛选杭州地区的预警
hangzhou_warnings = [w for w in warnings if "杭州" in w['area_desc']]
```

### 2. 按预警等级筛选

```python
# 筛选红色预警（最高级别）
red_warnings = [w for w in warnings if w['severity'] == 'Red']
```

### 3. 统计分析

```python
# 统计不同类型预警数量
disaster_types = {}
for warning in warnings:
    disaster_name = warning['disaster_name']
    disaster_types[disaster_name] = disaster_types.get(disaster_name, 0) + 1
```

### 4. 连续监控

```python
import time

crawler = WeatherWarningCrawler()
while True:
    raw_data = crawler.get_warning_data()
    if raw_data:
        warnings = crawler.parse_warning_data(raw_data)
        print(f"当前共有 {len(warnings)} 条预警")
    
    time.sleep(300)  # 每5分钟检查一次
```

## 文件说明

- `weather_warning_crawler.py` - 完整功能爬虫代码
- `simple_crawler.py` - 简化版爬虫代码（推荐日常使用）
- `example_usage.py` - 详细使用示例
- `requirements.txt` - 依赖包列表
- `README.md` - 说明文档

## 两个版本的区别

### 完整版 (weather_warning_crawler.py)
- 功能全面，支持数据分析、筛选、监控等
- 代码结构完整，适合二次开发
- 输出格式丰富，支持CSV和JSON

### 简化版 (simple_crawler.py)
- 代码简洁，易于理解和修改
- 快速获取和显示预警信息
- 适合日常使用和快速查看

## 注意事项

1. **请求频率**: 建议控制请求频率，避免对服务器造成过大压力
2. **数据时效性**: 预警信息具有时效性，请及时处理获取的数据
3. **网络环境**: 确保网络连接稳定，建议在稳定的网络环境下运行
4. **合规使用**: 请遵守相关法律法规，仅用于学习和研究目的

## 常见问题

### Q: 为什么有时候获取不到数据？
A: 可能的原因：
- 网络连接问题
- 服务器临时不可用
- API参数变化

### Q: 如何处理编码问题？
A: 代码已经处理了中文编码问题，保存CSV时使用了`utf-8-sig`编码。

### Q: 可以获取历史数据吗？
A: 当前API主要提供实时和近期的预警数据，历史数据需要其他方式获取。

## 更新日志

- v1.0.0 (2025-07-14)
  - 初始版本发布
  - 支持基本的预警信息爬取
  - 支持CSV和JSON格式导出
  - 提供多种使用示例

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
