#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浙江省预警信息爬虫使用示例
"""

from weather_warning_crawler import WeatherWarningCrawler
import time


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建爬虫实例
    crawler = WeatherWarningCrawler()
    
    # 获取预警数据
    raw_data = crawler.get_warning_data()
    
    if raw_data:
        # 解析数据
        warnings = crawler.parse_warning_data(raw_data)
        
        # 打印摘要
        crawler.print_warnings_summary(warnings)
        
        # 保存数据
        if warnings:
            crawler.save_to_csv(warnings, "basic_warnings.csv")
            crawler.save_to_json(warnings, "basic_warnings.json")


def example_custom_parameters():
    """自定义参数示例"""
    print("\n=== 自定义参数示例 ===")
    
    crawler = WeatherWarningCrawler()
    
    # 使用不同的参数获取数据
    raw_data = crawler.get_warning_data(alarm_type=1, is_childrens=False)
    
    if raw_data:
        warnings = crawler.parse_warning_data(raw_data)
        crawler.print_warnings_summary(warnings)


def example_data_analysis():
    """数据分析示例"""
    print("\n=== 数据分析示例 ===")
    
    crawler = WeatherWarningCrawler()
    raw_data = crawler.get_warning_data()
    
    if raw_data:
        warnings = crawler.parse_warning_data(raw_data)
        
        if warnings:
            # 统计不同类型的预警
            disaster_types = {}
            severity_levels = {}
            senders = {}
            
            for warning in warnings:
                # 统计灾害类型
                disaster_name = warning['disaster_name']
                disaster_types[disaster_name] = disaster_types.get(disaster_name, 0) + 1
                
                # 统计预警等级
                severity = warning['severity']
                severity_levels[severity] = severity_levels.get(severity, 0) + 1
                
                # 统计发布机构
                sender = warning['sender']
                senders[sender] = senders.get(sender, 0) + 1
            
            print("\n--- 灾害类型统计 ---")
            for disaster, count in sorted(disaster_types.items(), key=lambda x: x[1], reverse=True):
                print(f"{disaster}: {count}条")
            
            print("\n--- 预警等级统计 ---")
            for severity, count in sorted(severity_levels.items(), key=lambda x: x[1], reverse=True):
                print(f"{severity}: {count}条")
            
            print("\n--- 发布机构统计 (前5名) ---")
            for sender, count in sorted(senders.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"{sender}: {count}条")


def example_continuous_monitoring():
    """连续监控示例"""
    print("\n=== 连续监控示例 ===")
    print("每5分钟检查一次预警信息更新...")
    
    crawler = WeatherWarningCrawler()
    previous_count = 0
    
    for i in range(3):  # 示例只运行3次
        print(f"\n第{i+1}次检查...")
        
        raw_data = crawler.get_warning_data()
        if raw_data:
            current_count = raw_data.get('total', 0)
            
            if current_count != previous_count:
                print(f"预警信息有更新！当前共{current_count}条预警")
                warnings = crawler.parse_warning_data(raw_data)
                
                # 保存带时间戳的文件
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                crawler.save_to_csv(warnings, f"warnings_monitor_{timestamp}.csv")
                
                previous_count = current_count
            else:
                print(f"预警信息无变化，当前共{current_count}条预警")
        
        if i < 2:  # 最后一次不需要等待
            print("等待5分钟...")
            time.sleep(300)  # 实际使用时可以设置为300秒(5分钟)


def example_filter_by_area():
    """按地区筛选示例"""
    print("\n=== 按地区筛选示例 ===")
    
    crawler = WeatherWarningCrawler()
    raw_data = crawler.get_warning_data()
    
    if raw_data:
        warnings = crawler.parse_warning_data(raw_data)
        
        # 筛选包含"杭州"的预警
        hangzhou_warnings = [w for w in warnings if "杭州" in w['area_desc']]
        
        print(f"杭州地区预警信息 (共{len(hangzhou_warnings)}条):")
        for warning in hangzhou_warnings:
            print(f"- {warning['headline']}")
            print(f"  区域: {warning['area_desc']}")
            print(f"  时间: {warning['send_time']}")
            print()


if __name__ == "__main__":
    # 运行所有示例
    example_basic_usage()
    example_custom_parameters()
    example_data_analysis()
    example_filter_by_area()
    
    # 注意：连续监控示例会运行较长时间，可以单独运行
    # example_continuous_monitoring()
